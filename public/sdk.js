//挂载一个对象到window上
/**
 * @param {Object} option
 * @param {String} option.container 挂载的容器
 * @param {Object} option.iframeStyle iframe的样式
 * @param {Object} option.controls 控制器
 */
;(function (global) {
  let iframe = document.createElement("iframe");
  window.addEventListener("message", receiveMessage, false);
  let styleElement = null;
  let cssLoaded = false; // 标记CSS是否已加载
  // 默认的iframe样式配置
  // 默认的iframe样式配置
  const defaultIframeStyles = {
    position: "fixed",
    width: "392px",
    height: "632px",
    bottom: "32px",
    right: "-408px",
    zIndex: "99",
    transition: "right 0.3s ease-in-out",
    border: "none",
    // borderRadius: "8px",
    // boxShadow: "0px 2px 14px 0px rgba(137, 137, 140, 0.15)",
  };

  // 移动端样式配置
  const mobileIframeStyles = {
    width: "100%",
    height: "100%",
    bottom: "0",
    right: "-100%",
  };

  // 低高度屏幕样式配置
  const lowHeightStyles = {
    bottom: "0",
    height: "100%",
  };

  // 创建并注入CSS样式
  function injectIframeStyles(customStyles = {}) {
    // 如果已存在样式元素，先移除
    if (styleElement) {
      document.head.removeChild(styleElement);
    }

    // 合并自定义样式
    const finalStyles = { ...defaultIframeStyles, ...customStyles };

    // 生成CSS规则
    const cssRules = [];

    // 基础样式
    const baseStyleProps = Object.entries(finalStyles)
      .map(([key, value]) => `${camelToKebab(key)}: ${value}`)
      .join("; ");

    cssRules.push(`.fsLiveChat { ${baseStyleProps}; }`);

    // 显示状态样式
    cssRules.push(`.fsLiveChat.show { right: 68px; opacity: 1; }`);

    // 移动端媒体查询
    const mobileStyleProps = Object.entries(mobileIframeStyles)
      .map(([key, value]) => `${camelToKebab(key)}: ${value}`)
      .join("; ");

    cssRules.push(`
      @media (max-width: 768px) {
        .fsLiveChat {
          ${mobileStyleProps};
        }
        .fsLiveChat.show {
          right: 0;
          z-index: 99999;
        }
      }
    `);

    // 低高度屏幕媒体查询
    const lowHeightStyleProps = Object.entries(lowHeightStyles)
      .map(([key, value]) => `${camelToKebab(key)}: ${value}`)
      .join("; ");

    cssRules.push(`
      @media (max-height: 700px) {
        .fsLiveChat {
          ${lowHeightStyleProps};
        }
      }
    `);

    // 创建style元素并注入
    styleElement = document.createElement("style");
    styleElement.type = "text/css";
    styleElement.setAttribute("data-livechat-styles", "true");
    styleElement.textContent = cssRules.join("\n");
    document.head.appendChild(styleElement);
  }

  // 驼峰转短横线命名
  function camelToKebab(str) {
    return str.replace(/([A-Z])/g, "-$1").toLowerCase();
  }

  // 动态加载CSS文件
  function loadCSSFile(cssUrl, callback) {
    // 检查是否已经加载过该CSS文件
    const existingLink = document.querySelector(`link[href="${cssUrl}"]`);
    if (existingLink) {
      if (callback) callback(null, 'already-loaded');
      return;
    }

    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.type = 'text/css';
    link.href = cssUrl;
    link.setAttribute('data-livechat-css', 'true');

    // 加载成功回调
    link.onload = function() {
      cssLoaded = true;
      if (callback) callback(null, 'loaded');
    };

    // 加载失败回调
    link.onerror = function() {
      console.warn('LiveChat CSS file failed to load:', cssUrl);
      if (callback) callback(new Error('CSS load failed'), null);
    };

    document.head.appendChild(link);
  }

  // 获取CSS文件的完整URL
  function getCSSFileUrl() {
    // 尝试从当前脚本标签获取基础路径
    const scripts = document.getElementsByTagName('script');
    let basePath = '';

    for (let i = 0; i < scripts.length; i++) {
      const src = scripts[i].src;
      if (src && src.includes('sdk.js')) {
        basePath = src.substring(0, src.lastIndexOf('/') + 1);
        break;
      }
    }

    // 如果没有找到脚本路径，使用相对路径
    if (!basePath) {
      basePath = './';
    }

    return basePath + 'iframe-styles.css';
  }


  // 处理接收到的消息
  function receiveMessage(event) {
    if (event.data === "hide_chat") {
      document.querySelector("iframe.fsLiveChat").className = "fsLiveChat";
    }
    if (event.data?.type === "open_chat") {
      document.querySelector("iframe.fsLiveChat").className = "fsLiveChat show";
    }
  }
  function getDooringApiStr(opt) {
    let controls = Object.assign(
      {
        //按照实际需求新增
      },
      opt || {}
    );
    if (["ru"].includes(controls["webSite"])) {
      controls["webSite"] = "en";
      controls["isoCode"] = "US";
      controls["language"] = 5;
    }
    let params = "";
    for (let key in controls) {
      params += key + "=" + encodeURI(controls[key]) + "&";
    }
    return params.slice(0, params.length - 1);
  }

  global.fsLiveChatMount = function (option) {
    this.option = option;
    // const sdk_domain_path = '$_path' //项目地址
    const sdk_domain_path = "http://10.28.86.17:5174"; //项目地址

    // 动态加载CSS文件
    // const cssUrl = getCSSFileUrl();
    // loadCSSFile(cssUrl, function(error, status) {
    //   if (error) {
    //     console.warn('LiveChat: CSS文件加载失败，将使用内联样式作为备用方案');
    //     // 如果CSS文件加载失败，使用原有的内联样式注入方式作为备用
    //     const customStyles = option && option.iframeStyle ? option.iframeStyle : {};
    //     injectIframeStyles(customStyles);
    //   } else {
    //     console.log('LiveChat: CSS文件加载成功 -', status);
    //     // CSS文件加载成功后，如果有自定义样式，仍然需要注入
    //     if (option && option.iframeStyle && Object.keys(option.iframeStyle).length > 0) {
    //       injectIframeStyles(option.iframeStyle);
    //     }
    //   }
    // });

    iframe.src = sdk_domain_path + "?" + getDooringApiStr(option);
    iframe.allow = "geolocation";
    iframe.style.border = "none";
    iframe.className = "fsLiveChat";
    iframe.title = "FsLiveChat";


    document.querySelector(option.container || "body").appendChild(iframe);
  };
  //卸载
  global.fsLiveChatUnmount = function () {
    document.querySelector(this.option.container || "body").removeChild(iframe);
    window.removeEventListener("message", receiveMessage, false);

    // 清理注入的内联样式
    if (styleElement && document.head.contains(styleElement)) {
      document.head.removeChild(styleElement);
      styleElement = null;
    }

    // 清理动态加载的CSS文件
    const cssLinks = document.querySelectorAll('link[data-livechat-css="true"]');
    cssLinks.forEach(link => {
      if (document.head.contains(link)) {
        document.head.removeChild(link);
      }
    });

    // 重置CSS加载状态
    cssLoaded = false;
  };
  //显示
  global.showFsLiveChat = function () {
    document
      .querySelector("iframe.fsLiveChat")
      .contentWindow.postMessage(
        { type: "open_chat", origin: window.location.href },
        "*"
      );
    document.querySelector("iframe.fsLiveChat").className = "fsLiveChat show";
  };
  //隐藏
  global.hideFsLiveChat = function () {
    document
      .querySelector("iframe.fsLiveChat")
      .contentWindow.postMessage("hide_chat", "*");
    document.querySelector("iframe.fsLiveChat").className = "fsLiveChat";
  };
  //
  global.postDataToChat = function (payload) {
    document
      .querySelector("iframe.fsLiveChat")
      .contentWindow.postMessage(
        { type: payload.type, data: payload.data },
        "*"
      );
  };
})(window)
